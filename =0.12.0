Requirement already satisfied: Appium-Python-Client in ./venv/lib/python3.13/site-packages (5.1.1)
Requirement already satisfied: webdriver-manager in ./venv/lib/python3.13/site-packages (4.0.2)
Requirement already satisfied: selenium in ./venv/lib/python3.13/site-packages (4.31.0)
Requirement already satisfied: facebook-wda in ./venv/lib/python3.13/site-packages (1.5.0)
Requirement already satisfied: tidevice in ./venv/lib/python3.13/site-packages (0.12.10)
Requirement already satisfied: requests in ./venv/lib/python3.13/site-packages (from webdriver-manager) (2.32.3)
Requirement already satisfied: python-dotenv in ./venv/lib/python3.13/site-packages (from webdriver-manager) (1.1.0)
Requirement already satisfied: packaging in ./venv/lib/python3.13/site-packages (from webdriver-manager) (24.2)
Requirement already satisfied: urllib3<3,>=1.26 in ./venv/lib/python3.13/site-packages (from urllib3[socks]<3,>=1.26->selenium) (2.4.0)
Requirement already satisfied: trio~=0.17 in ./venv/lib/python3.13/site-packages (from selenium) (0.29.0)
Requirement already satisfied: trio-websocket~=0.9 in ./venv/lib/python3.13/site-packages (from selenium) (0.12.2)
Requirement already satisfied: certifi>=2021.10.8 in ./venv/lib/python3.13/site-packages (from selenium) (2025.1.31)
Requirement already satisfied: typing_extensions~=4.9 in ./venv/lib/python3.13/site-packages (from selenium) (4.13.2)
Requirement already satisfied: websocket-client~=1.8 in ./venv/lib/python3.13/site-packages (from selenium) (1.8.0)
Requirement already satisfied: six in ./venv/lib/python3.13/site-packages (from facebook-wda) (1.16.0)
Requirement already satisfied: retry in ./venv/lib/python3.13/site-packages (from facebook-wda) (0.9.2)
Requirement already satisfied: Pillow in ./venv/lib/python3.13/site-packages (from facebook-wda) (11.2.1)
Requirement already satisfied: cached-property~=1.5.1 in ./venv/lib/python3.13/site-packages (from facebook-wda) (1.5.2)
Requirement already satisfied: Deprecated~=1.2.6 in ./venv/lib/python3.13/site-packages (from facebook-wda) (1.2.18)
Requirement already satisfied: construct>=2 in ./venv/lib/python3.13/site-packages (from facebook-wda) (2.10.70)
Requirement already satisfied: colored in ./venv/lib/python3.13/site-packages (from tidevice) (2.3.0)
Requirement already satisfied: tornado in ./venv/lib/python3.13/site-packages (from tidevice) (6.4.2)
Requirement already satisfied: simple-tornado>=0.2.2 in ./venv/lib/python3.13/site-packages (from tidevice) (0.2.2)
Requirement already satisfied: simplejson in ./venv/lib/python3.13/site-packages (from tidevice) (3.20.1)
Requirement already satisfied: tabulate in ./venv/lib/python3.13/site-packages (from tidevice) (0.9.0)
Requirement already satisfied: logzero in ./venv/lib/python3.13/site-packages (from tidevice) (1.7.0)
Requirement already satisfied: deprecation in ./venv/lib/python3.13/site-packages (from tidevice) (2.1.0)
Requirement already satisfied: wrapt<2,>=1.10 in ./venv/lib/python3.13/site-packages (from Deprecated~=1.2.6->facebook-wda) (1.17.2)
Requirement already satisfied: attrs>=23.2.0 in ./venv/lib/python3.13/site-packages (from trio~=0.17->selenium) (25.3.0)
Requirement already satisfied: sortedcontainers in ./venv/lib/python3.13/site-packages (from trio~=0.17->selenium) (2.4.0)
Requirement already satisfied: idna in ./venv/lib/python3.13/site-packages (from trio~=0.17->selenium) (3.10)
Requirement already satisfied: outcome in ./venv/lib/python3.13/site-packages (from trio~=0.17->selenium) (1.3.0.post0)
Requirement already satisfied: sniffio>=1.3.0 in ./venv/lib/python3.13/site-packages (from trio~=0.17->selenium) (1.3.1)
Requirement already satisfied: wsproto>=0.14 in ./venv/lib/python3.13/site-packages (from trio-websocket~=0.9->selenium) (1.2.0)
Requirement already satisfied: pysocks!=1.5.7,<2.0,>=1.5.6 in ./venv/lib/python3.13/site-packages (from urllib3[socks]<3,>=1.26->selenium) (1.7.1)
Requirement already satisfied: charset-normalizer<4,>=2 in ./venv/lib/python3.13/site-packages (from requests->webdriver-manager) (3.4.1)
Requirement already satisfied: decorator>=3.4.2 in ./venv/lib/python3.13/site-packages (from retry->facebook-wda) (5.2.1)
Requirement already satisfied: py<2.0.0,>=1.4.26 in ./venv/lib/python3.13/site-packages (from retry->facebook-wda) (1.11.0)
Requirement already satisfied: h11<1,>=0.9.0 in ./venv/lib/python3.13/site-packages (from wsproto>=0.14->trio-websocket~=0.9->selenium) (0.16.0)
