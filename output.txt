2025-07-25 19:54:49,777 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-25 19:54:49,778 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-25 19:54:49,778 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-25 19:54:49,779 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-25 19:54:49,779 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-25 19:54:49,780 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-25 19:54:49,780 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-25 19:54:49,780 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-25 19:54:49,781 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-25 19:54:49,781 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-25 19:54:49,781 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-25 19:54:49,782 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-07-25 19:54:49,782 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-07-25 19:54:51,856 - __main__ - INFO - Existing processes terminated
2025-07-25 19:54:52,736 - utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
2025-07-25 19:54:52,737 - utils.global_values_db - INFO - Global values database initialized successfully
2025-07-25 19:54:52,737 - utils.global_values_db - INFO - Using global values from config.py
2025-07-25 19:54:52,737 - utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-07-25 19:54:52,739 - utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-07-25 19:54:52,739 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-25 19:54:52,770 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-07-25 19:54:53,104 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-07-25 19:54:53,105 - utils.database - INFO - Test_steps table schema updated successfully
2025-07-25 19:54:53,105 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-07-25 19:54:53,106 - utils.database - INFO - Screenshots table schema updated successfully
2025-07-25 19:54:53,106 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-07-25 19:54:53,106 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-07-25 19:54:53,106 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-07-25 19:54:53,106 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-07-25 19:54:53,107 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-07-25 19:54:53,107 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-07-25 19:54:53,107 - utils.database - INFO - Database initialized successfully
2025-07-25 19:54:53,107 - utils.database - INFO - Checking initial database state...
2025-07-25 19:54:53,108 - utils.database - INFO - Database state: 0 suites, 0 cases, 11269 steps, 0 screenshots, 0 tracking entries
2025-07-25 19:54:53,128 - app - INFO - Using directories from config.py:
2025-07-25 19:54:53,128 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-25 19:54:53,128 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-25 19:54:53,128 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-25 19:54:53,259] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-25 19:54:53,272] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x11a552e40>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-07-25 19:54:53,272] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-07-25 19:54:53,312] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-07-25 19:54:53,353] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-07-25 19:54:55,356] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-07-25 19:54:55,356] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-07-25 19:54:56,180] INFO in appium_device_controller: Installed Appium drivers: 
[2025-07-25 19:54:56,181] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-07-25 19:54:57,055] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-07-25 19:54:57,055] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-07-25 19:54:57,055] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-07-25 19:54:57,061] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[2025-07-25 19:54:59,078] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:54:59,078] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:01,090] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:01,090] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:03,097] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:03,097] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:05,108] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:05,108] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:07,119] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:07,119] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:09,128] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:09,128] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:11,136] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:11,136] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:13,144] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:13,145] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:15,153] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:15,153] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:17,162] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:17,162] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:19,169] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:19,169] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:21,176] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:21,176] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:23,183] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:23,183] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:25,190] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:25,190] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:27,198] INFO in appium_device_controller: Appium server started successfully
[2025-07-25 19:55:27,198] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
Starting Mobile App Automation Tool...
Configuration:
  - Flask server port: 8080
  - Appium server port: 4723
  - WebDriverAgent port: 8100
Open your web browser and navigate to: http://localhost:8080
 * Serving Flask app 'app'
 * Debug mode: on
[2025-07-25 19:55:27,222] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://************:8080
[2025-07-25 19:55:27,222] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-07-25 19:55:29,796] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "GET / HTTP/1.1" 200 -
[2025-07-25 19:55:29,838] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,840] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,846] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,847] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,847] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,848] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,850] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,851] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,856] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/css/test-case-modification.css HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,857] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,859] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,861] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,864] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,865] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,866] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,877] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,878] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,879] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "GET /static/js/action-manager.js?v=1753440329 HTTP/1.1" 200 -
[2025-07-25 19:55:29,880] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,884] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,887] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,891] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,895] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,898] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,907] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,909] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,910] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,913] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "GET /static/js/main.js?v=1753440329 HTTP/1.1" 200 -
[2025-07-25 19:55:29,914] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,917] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,920] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,924] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/import-export.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,927] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,930] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/js/test-case-modification.js HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,950] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-07-25 19:55:29,982] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-25 19:55:29,989] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-25 19:55:29,990] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "GET /api/environments HTTP/1.1" 200 -
[2025-07-25 19:55:29,997] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:29] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-25 19:55:29,998] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[2025-07-25 19:55:30,016] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/settings HTTP/1.1" 200 -
[2025-07-25 19:55:30,022] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-07-25 19:55:30,025] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:55:30,039] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-25 19:55:30,048] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/environments/current HTTP/1.1" 200 -
[2025-07-25 19:55:30,049] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-07-25 19:55:30,057] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-07-25 19:55:30,065] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-25 19:55:30,076] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-25 19:55:30,085] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-25 19:55:30,091] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/environments/5/variables HTTP/1.1" 200 -
[2025-07-25 19:55:30,096] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-25 19:55:30,143] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-07-25 19:55:30,154] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/test_cases/action_types HTTP/1.1" 200 -
[2025-07-25 19:55:30,211] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/test_cases/locator_types HTTP/1.1" 200 -
[2025-07-25 19:55:30,226] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-25 19:55:30,227] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-25 19:55:30,263] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:30] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-25 19:55:31,739] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-25 19:55:31,744] INFO in appium_device_controller: Appium server is running and ready
[2025-07-25 19:55:31,745] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-25 19:55:31,745] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:31] "GET /api/devices HTTP/1.1" 200 -
[2025-07-25 19:55:33,614] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-25 19:55:33,617] INFO in appium_device_controller: Appium server is running and ready
[2025-07-25 19:55:33,617] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-25 19:55:33,617] INFO in appium_device_controller: Connecting to device: 00008120-00186C801E13C01E with options: None, platform hint: iOS
[2025-07-25 19:55:33,617] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-25 19:55:33,618] INFO in appium_device_controller: Using provided platform hint: iOS
[2025-07-25 19:55:33,618] INFO in appium_device_controller: Using custom WebDriverAgent URL: http://localhost:8100
[2025-07-25 19:55:33,618] INFO in appium_device_controller: Desired capabilities: {'platformName': 'iOS', 'deviceName': '00008120-00186C801E13C01E', 'udid': '00008120-00186C801E13C01E', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'XCUITest', 'xcodeOrgId': '', 'xcodeSigningId': 'iPhone Developer', 'webDriverAgentUrl': 'http://localhost:8100', 'showIOSLog': True}
[2025-07-25 19:55:33,618] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True}
[2025-07-25 19:55:33,618] INFO in appium_device_controller: Connecting to iOS device via WebDriverAgent
[2025-07-25 19:55:33,636] INFO in appium_device_controller: Device 00008120-00186C801E13C01E is listed and trusted
[2025-07-25 19:55:33,638] INFO in appium_device_controller: Found port 8100 for device 00008120-00186C801E13C01E in wda_ports.txt
[2025-07-25 19:55:33,638] INFO in appium_device_controller: Using WebDriverAgent URL: http://localhost:8100 for device 00008120-00186C801E13C01E
[2025-07-25 19:55:33,646] INFO in appium_device_controller: WebDriverAgent is already running at http://localhost:8100
[2025-07-25 19:55:33,646] INFO in appium_device_controller: WebDriverAgent status: {'value': {'build': {'version': '9.5.0', 'time': 'Jun  8 2025 18:35:21', 'productBundleIdentifier': 'com.facebook.WebDriverAgentRunner'}, 'os': {'testmanagerdVersion': 65535, 'name': 'iOS', 'sdkVersion': '18.4', 'version': '18.5'}, 'device': 'iphone', 'ios': {'ip': '************'}, 'message': 'WebDriverAgent is ready to accept commands', 'state': 'success', 'ready': True}, 'sessionId': 'BDB8C8EF-BF9E-4298-898C-92D77D79077D'}
[2025-07-25 19:55:33,649] INFO in appium_device_controller: Appium server is already running
[2025-07-25 19:55:33,649] INFO in appium_device_controller: iOS connection attempt 1/3
[2025-07-25 19:55:33,650] INFO in appium_device_controller: Using capabilities: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True, 'webDriverAgentUrl': 'http://localhost:8100'}
[2025-07-25 19:55:33,654] INFO in appium_device_controller: Appium server status before connection: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '449e6c8719c5b3ff404114b389ca9adf960b42b0', 'built': '2025-07-25 17:37:53 +1000'}}}
[2025-07-25 19:55:33,654] INFO in appium_device_controller: Connecting to Appium server at http://127.0.0.1:4723/wd/hub
[2025-07-25 19:55:34,403] INFO in appium_device_controller: Wrapping driver with Healenium self-healing capabilities
[2025-07-25 19:55:34,406] INFO in appium_device_controller: Driver successfully wrapped with Healenium
[2025-07-25 19:55:34,407] INFO in appium_device_controller: Successfully connected to iOS device
[2025-07-25 19:55:34,407] INFO in appium_device_controller: Connected with session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 19:55:34,407] INFO in appium_device_controller: Connection verified with capabilities: iOS
[2025-07-25 19:55:34,407] INFO in appium_device_controller: Initializing platform helpers for iOS
[2025-07-25 19:55:34,407] INFO in appium_device_controller: Getting device dimensions
[2025-07-25 19:55:34,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:55:34,982] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:55:35,336] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-07-25 19:55:35,336] INFO in appium_device_controller: Device dimensions: (393, 852)
[2025-07-25 19:55:35,343] WARNING in appium_device_controller: Failed to initialize ImageMatcher: No module named 'app.utils'; 'app' is not a package
[2025-07-25 19:55:35,343] INFO in appium_device_controller: Initializing iOS-specific helpers
[2025-07-25 19:55:35,343] INFO in appium_device_controller: Setting up iOS predicate string and class chain support
[2025-07-25 19:55:35,343] INFO in appium_device_controller: iOS version: 18.0
[2025-07-25 19:55:35,343] INFO in appium_device_controller: Using modern keyboard handling for iOS 15+
[2025-07-25 19:55:35,343] INFO in appium_device_controller: Platform helpers initialization completed
[2025-07-25 19:55:35,343] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-07-25 19:55:35,344] INFO in action_factory: Registered basic actions: tap, wait
[2025-07-25 19:55:35,345] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-07-25 19:55:35,345] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-07-25 19:55:35,346] INFO in action_factory: Registered action handler for 'multiStep'
[2025-07-25 19:55:35,347] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-07-25 19:55:35,347] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-07-25 19:55:35,348] INFO in action_factory: Registered action handler for 'swipe'
[2025-07-25 19:55:35,349] INFO in action_factory: Registered action handler for 'getParam'
[2025-07-25 19:55:35,350] INFO in action_factory: Registered action handler for 'wait'
[2025-07-25 19:55:35,350] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-07-25 19:55:35,351] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-07-25 19:55:35,351] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-07-25 19:55:35,352] INFO in action_factory: Registered action handler for 'text'
[2025-07-25 19:55:35,353] ERROR in action_factory: Failed to import module for tap_if_text_exists_action
[2025-07-25 19:55:35,355] INFO in action_factory: Registered action handler for 'waitTill'
[2025-07-25 19:55:35,356] INFO in action_factory: Registered action handler for 'hookAction'
[2025-07-25 19:55:35,360] ERROR in action_factory: Error loading action handler from input_text_action: invalid syntax (input_text_action.py, line 226)
[2025-07-25 19:55:35,361] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-07-25 19:55:35,361] INFO in global_values_db: Global values database initialized successfully
[2025-07-25 19:55:35,361] INFO in global_values_db: Using global values from config.py
[2025-07-25 19:55:35,362] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-07-25 19:55:35,364] INFO in action_factory: Registered action handler for 'setParam'
[2025-07-25 19:55:35,364] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-07-25 19:55:35,364] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-07-25 19:55:35,365] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-07-25 19:55:35,365] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-07-25 19:55:35,366] INFO in action_factory: Registered action handler for 'clickImage'
[2025-07-25 19:55:35,367] INFO in action_factory: Registered action handler for 'tap'
[2025-07-25 19:55:35,368] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-07-25 19:55:35,368] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-07-25 19:55:35,369] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-07-25 19:55:35,369] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-07-25 19:55:35,370] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-07-25 19:55:35,371] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-07-25 19:55:35,371] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-07-25 19:55:35,372] INFO in action_factory: Registered action handler for 'launchApp'
[2025-07-25 19:55:35,372] INFO in action_factory: Registered action handler for 'ifThenSteps'
[2025-07-25 19:55:35,373] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-07-25 19:55:35,373] INFO in action_factory: Registered action handler for 'info'
[2025-07-25 19:55:35,374] INFO in action_factory: Registered action handler for 'waitElement'
[2025-07-25 19:55:35,375] INFO in action_factory: Registered action handler for 'compareValue'
[2025-07-25 19:55:35,376] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-07-25 19:55:35,377] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-07-25 19:55:35,377] INFO in action_factory: Registered action handler for 'exists'
[2025-07-25 19:55:35,377] INFO in action_factory: Registered action handler for 'clickElement'
[2025-07-25 19:55:35,378] INFO in action_factory: Registered action handler for 'randomData'
[2025-07-25 19:55:35,378] INFO in action_factory: Registered action handler for 'getValue'
[2025-07-25 19:55:35,379] INFO in action_factory: Registered action handler for 'test'
[2025-07-25 19:55:35,379] INFO in action_factory: Registered action handler for 'restartApp'
[2025-07-25 19:55:35,380] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-07-25 19:55:35,380] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-07-25 19:55:35,380] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'ifThenSteps', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-07-25 19:55:35,380] INFO in action_factory: Handler for 'tap': TapAction
[2025-07-25 19:55:35,380] INFO in action_factory: Handler for 'wait': WaitAction
[2025-07-25 19:55:35,380] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-07-25 19:55:35,380] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-07-25 19:55:35,380] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-07-25 19:55:35,380] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-07-25 19:55:35,380] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-07-25 19:55:35,380] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-07-25 19:55:35,380] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-07-25 19:55:35,380] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-07-25 19:55:35,380] INFO in action_factory: Handler for 'text': TextAction
[2025-07-25 19:55:35,380] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'ifThenSteps': IfThenStepsAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'info': InfoAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'test': TestAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-07-25 19:55:35,381] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-07-25 19:55:35,381] INFO in appium_device_controller: Initializing Airtest connection for device: 00008120-00186C801E13C01E...
[2025-07-25 19:55:35,383] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8100
[2025-07-25 19:55:35,388] INFO in ios_device: Initialized MinimalIOSDevice for 00008120-00186C801E13C01E with WDA at http://localhost:8100
[2025-07-25 19:55:35,393] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-07-25 19:55:35,393] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-25 19:55:35,393] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-25 19:55:35,393] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-07-25 19:55:35,626] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-25 19:55:36,543] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:36] "POST /api/device/connect HTTP/1.1" 200 -
[2025-07-25 19:55:37,557] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-25 19:55:37,558] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-07-25 19:55:37,834] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-07-25 19:55:37,834] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:37] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1753437329975_fxh2i5bj1_1753436415877_hqxy84if0&t=1753437337554 HTTP/1.1" 200 -
[2025-07-25 19:55:39,975] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:55:39,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:55:40,174] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:40] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-07-25 19:55:40,178] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:40] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-07-25 19:55:41,793] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:41] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-07-25 19:55:41,795] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:41] "GET /api/test_suites/90853884-1b79-4f05-8542-f590d5d307a1 HTTP/1.1" 200 -
[2025-07-25 19:55:41,804] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:41] "GET /api/test_cases/load/Browse__PDP_20250510095542.json HTTP/1.1" 200 -
[2025-07-25 19:55:41,809] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:41] "GET /api/test_cases/load/Delivery__CNC_20250505163250.json HTTP/1.1" 200 -
[2025-07-25 19:55:41,815] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:41] "GET /api/test_cases/load/All_Sign_ins_20250501131834.json HTTP/1.1" 200 -
[2025-07-25 19:55:41,821] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:41] "GET /api/test_cases/load/WishList_20250510110236.json HTTP/1.1" 200 -
[2025-07-25 19:55:41,826] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:41] "GET /api/test_cases/load/KmartProdSignin_20250426221008.json HTTP/1.1" 200 -
[2025-07-25 19:55:41,831] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:41] "GET /api/test_cases/load/AU_MyAccount_20250506181929.json HTTP/1.1" 200 -
[2025-07-25 19:55:41,836] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:41] "GET /api/test_cases/load/Others_20250512190312.json HTTP/1.1" 200 -
[2025-07-25 19:55:41,841] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:41] "GET /api/test_cases/load/Postcode_Flow_20250502104451.json HTTP/1.1" 200 -
[2025-07-25 19:55:41,846] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:41] "GET /api/test_cases/load/App_Settings_AU_20250609145542.json HTTP/1.1" 200 -
[2025-07-25 19:55:41,851] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:41] "GET /api/test_cases/load/temp_20250615085036.json HTTP/1.1" 200 -
[2025-07-25 19:55:41,863] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:41] "GET /api/test_cases/load/All_Payments_Check_20250512194232.json HTTP/1.1" 200 -
[2025-07-25 19:55:44,976] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:44] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:55:44,980] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:44] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:55:49,976] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:55:49,980] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:55:54,975] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:55:54,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:55:59,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:59] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:55:59,982] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:55:59] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:04,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:04,981] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:06,559] DEBUG in appium_device_controller: Session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 19:56:06,559] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-07-25 19:56:06,559] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:06] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-25 19:56:09,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:09,982] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:14,975] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:14,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:19,975] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:19,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:24,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:24,981] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:29,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:29,980] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:34,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:36,557] DEBUG in appium_device_controller: Session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 19:56:36,558] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-07-25 19:56:36,558] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:36] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-25 19:56:39,975] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:44,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:44] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:49,976] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:54,976] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:56:59,975] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:56:59] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:57:04,976] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:57:06,557] DEBUG in appium_device_controller: Session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 19:57:06,557] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-07-25 19:57:06,558] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:06] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-25 19:57:09,976] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:57:14,976] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:57:19,976] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:57:24,975] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:57:29,976] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:57:34,975] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:57:36,557] DEBUG in appium_device_controller: Session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 19:57:36,558] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-07-25 19:57:36,558] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:36] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-25 19:57:39,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:57:44,976] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:44] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:57:49,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:57:54,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:57:59,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:57:59] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:58:04,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:58:06,559] DEBUG in appium_device_controller: Session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 19:58:06,559] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-07-25 19:58:06,560] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:06] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-25 19:58:09,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:58:14,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:58:19,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:58:24,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:58:29,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:58:34,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:58:36,558] DEBUG in appium_device_controller: Session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 19:58:36,558] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-07-25 19:58:36,558] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:36] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-25 19:58:39,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:58:44,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:44] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:58:49,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:58:54,980] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:58:59,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:58:59] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:59:04,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:59:06,559] DEBUG in appium_device_controller: Session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 19:59:06,559] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-07-25 19:59:06,560] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:06] "GET /api/session/health HTTP/1.1" 200 -
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===[2025-07-25 19:59:09,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:59:14,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:59:19,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:59:24,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:59:29,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:59:34,977] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:59:36,558] DEBUG in appium_device_controller: Session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 19:59:36,558] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-07-25 19:59:36,559] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:36] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-25 19:59:39,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:59:44,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:44] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:59:49,980] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:59:55,007] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:55] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 19:59:59,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 19:59:59] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:00:04,980] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:00:06,560] DEBUG in appium_device_controller: Session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 20:00:06,561] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-07-25 20:00:06,561] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:06] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-25 20:00:09,980] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:00:14,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:00:19,980] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:00:24,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:00:29,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:00:34,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:00:36,560] DEBUG in appium_device_controller: Session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 20:00:36,560] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-07-25 20:00:36,560] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:36] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-25 20:00:39,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:00:44,981] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:44] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:00:49,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:00:54,978] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:00:59,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:00:59] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:01:04,981] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:01:06,560] DEBUG in appium_device_controller: Session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 20:01:06,560] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-07-25 20:01:06,560] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:06] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-25 20:01:09,980] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:01:14,981] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:01:19,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:01:24,980] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:24] "GET /api/reports/latest HTTP/1.1" 200 -

=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===[2025-07-25 20:01:29,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:01:34,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:01:36,561] DEBUG in appium_device_controller: Session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 20:01:36,562] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-07-25 20:01:36,563] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:36] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-25 20:01:39,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:01:44,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:44] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:01:49,981] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:01:54,980] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:01:59,981] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:01:59] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:02:04,980] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:02:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:02:06,561] DEBUG in appium_device_controller: Session ID: 0db3f982-3e04-4177-b772-028389e6b118
[2025-07-25 20:02:06,561] DEBUG in appium_device_controller: Session is responsive (session status check passed)
[2025-07-25 20:02:06,561] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:02:06] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-25 20:02:09,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:02:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:02:14,981] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:02:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-25 20:02:19,979] INFO in _internal: 127.0.0.1 - - [25/Jul/2025 20:02:19] "GET /api/reports/latest HTTP/1.1" 200 -

=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/report_template.html ===
Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/run.py", line 274, in <module>
    app.run(debug=True, use_reloader=False, host='0.0.0.0', port=args.port)
    ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/flask/app.py", line 1191, in run
    run_simple(t.cast(str, host), port, self, **options)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/werkzeug/serving.py", line 1069, in run_simple
    srv.serve_forever()
    ~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/werkzeug/serving.py", line 766, in serve_forever
    super().serve_forever(poll_interval=poll_interval)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socketserver.py", line 235, in serve_forever
    ready = selector.select(poll_interval)
  File "/usr/local/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/selectors.py", line 398, in select
    fd_event_list = self._selector.poll(timeout)
TypeError: shutdown_handler() takes 0 positional arguments but 2 were given
